package com.uino.x.pedestal.twin.jrm.core.sql;


import com.uino.x.common.tool.base.StringUtils;
import com.uino.x.pedestal.twin.jrm.core.domain.ArraysProcessorOptional;
import com.uino.x.pedestal.twin.jrm.core.domain.JsonKey;
import com.uino.x.pedestal.twin.jrm.core.domain.JsonSql;
import com.uino.x.pedestal.twin.jrm.core.json.definition.JsonDefinition;
import com.uino.x.pedestal.twin.jrm.core.structure.AutoIncrementSortList;

import javax.sql.DataSource;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.uino.x.pedestal.twin.common.constant.Sql.SPACE;

/**
 * 抽象的sql处理器
 *
 * <AUTHOR>
 * @version 0.0.1
 * @date 2021/4/25 11:30
 */
public abstract class AbstractSqlBuilderProcessor implements SqlBuilderProcessor {

    /**
     * 默认的空{@link JsonSql}
     */
    protected static final JsonSql EMPTY_JSON_SQL = new JsonSql();

    /**
     * 用于拼接sql的{@link StringBuilder}
     */
    protected final StringBuilder sql = new StringBuilder();

    /**
     * {@link JsonSql}自增长列表
     */
    protected final AutoIncrementSortList<JsonSql> jsonSqlList;

    /**
     * json描述
     */
    protected final JsonDefinition jsonDefinition;

    /**
     * 数据源
     */
    protected final DataSource dataSource;

    /**
     * 构造方法
     *
     * @param dataSource     数据源
     * @param jsonDefinition json描述
     * <AUTHOR>
     * @date 2021/4/25 11:30
     */
    public AbstractSqlBuilderProcessor(DataSource dataSource, JsonDefinition jsonDefinition) {

        this.dataSource = dataSource;
        this.jsonDefinition = jsonDefinition;
        this.jsonSqlList = initJsonSqlList(jsonDefinition.getVersion());
    }

    @Override
    public AutoIncrementSortList<JsonSql> initJsonSqlList(Integer version) {

        return AutoIncrementSortList.emptyAutoIncrementSortList();
    }

    @Override
    public JsonDefinition jsonDefinition() {

        return this.jsonDefinition;
    }

    @Override
    public AutoIncrementSortList<JsonSql> jsonSqlList() {

        return this.jsonSqlList;
    }

    @Override
    public String convertJsonValueToSqlSegment(JsonSql jsonSql) {

        if (Objects.isNull(jsonSql)) {

            return null;
        }

        String path = jsonSql.getPath();
        if (StringUtils.isBlank(path)) {

            return null;
        }

        JsonKey jsonKey = jsonDefinition().getJsonKey(path);
        if (Objects.isNull(jsonKey)) {

            return null;
        }
        return String.valueOf(jsonKey.getValue());
    }

    @Override
    public String convertJsonValueToSqlSegment() {

        JsonSql jsonSql = jsonSqlList.get();
        return convertJsonValueToSqlSegment(jsonSql);
    }

    @Override
    public SqlBuilderProcessor append(boolean preCondition, StringBuilder sql, String attachPrefix, Object value, String attachSuffix) {

        if (preCondition && Objects.nonNull(value)) {

            sql.append(attachPrefix)
                    .append(value)
                    .append(attachSuffix);
        }
        return this;
    }

    @Override
    public DataSource dataSource() {

        return dataSource;
    }

    @Override
    public SqlBuilderProcessor append(Object value) {

        sql.append(value);
        return this;
    }

    @Override
    public void buildSql() {
        // 预准备操作
        operationPrepare();
        // 取出并规定具体的操作种类
        final JsonSql kindJsonSql = getKindJsonSql();
        String kindValue = kindJsonSql.getValue();
        if (Objects.nonNull(kindValue)) {
            // 如果kind的值是数组标识,说明要进行数组对应的处理
            if (arraysProcessor(kindJsonSql)) {
                return;
            }
            append(kindValue).append(SPACE);

            // 循环JsonSqlMapList去拼接sql,JsonSqlMap#value中有值即拼接,包括?占位符值的代入
            AutoIncrementSortList<JsonSql> jsonSqlList = jsonSqlList();

            for (JsonSql jsonSql : jsonSqlList) {
                if (arraysProcessor(jsonSql)) {
                    return;
                }
                placeholderProcessor(jsonSql, convertJsonValueToSqlSegment(jsonSql));
            }
        }
    }

    /**
     * 获取操作类别的{@link JsonSql}
     *
     * @return 操作类别的{@link JsonSql}
     * <AUTHOR>
     * @date 2021/4/25 11:30
     */
    protected JsonSql getKindJsonSql() {

        AutoIncrementSortList<JsonSql> jsonSqlList = jsonSqlList();
        JsonSql kindJsonSql = jsonSqlList.get();
        // 操作类别不存在,返回默认空的json sql
        if (Objects.isNull(kindJsonSql)) {
            return EMPTY_JSON_SQL;
        }
        // 递归获取操作类别
        String kindPath = kindJsonSql.getPath();
        if (!jsonDefinition().hasPath(kindPath)) {
            return getKindJsonSql();
        }
        jsonSqlList.removeIf(e -> !e.getPath().startsWith(kindPath));
        if (jsonSqlList.size() == 1) {

            final JsonSql first = jsonSqlList.getFirst();
            placeholderProcessor(jsonSqlList.getFirst(), convertJsonValueToSqlSegment(first));
            return EMPTY_JSON_SQL;
        }
        // 移除头节点
        jsonSqlList.removeFirst();
        // 恢复自增索引
        jsonSqlList.recoverIndex();
        return kindJsonSql;
    }

    /**
     * 数据操作种类处理
     *
     * @param kindJsonSql 操作类型{@link JsonSql}
     * @return 是否是数组标识
     * <AUTHOR>
     * @date 2021/4/25 11:30
     */
    protected boolean arraysProcessor(JsonSql kindJsonSql) {

        ArraysProcessorOptional arraysProcessorOptional = acquireArraysProcessorOptional();
        if (Objects.isNull(arraysProcessorOptional)) {
            return false;
        }
        final String arrayIdentifyValue = "[]";
        String kindJsonSqlMapValue = kindJsonSql.getValue();
        if (kindJsonSqlMapValue.equals(arrayIdentifyValue)) {

            append(arraysProcessorOptional.getOpen()).append(SPACE);
            String kindPath = kindJsonSql.getPath();
            // 获取整个kind下的数组对象
            JsonKey kindJsonKey = jsonDefinition().getJsonKey(kindPath);
            List<?> columnList = (List<?>) kindJsonKey.getValue();
            for (int i = 0; i < columnList.size(); i++) {

                // 遍历当前jsonSqlList
                for (JsonSql jsonSql : jsonSqlList()) {
                    @SuppressWarnings("unchecked")
                    Map<String, Object> obj = (Map<String, Object>) columnList.get(i);
                    // 递归处理json对象
                    jsonObjectRecursive(jsonSql, arrayIdentifyValue, kindPath, i, obj, null);
                }
                if (i < columnList.size() - 1) {

                    append(arraysProcessorOptional.getSeparator());
                }
            }
            append(arraysProcessorOptional.getClose());
            return true;
        } else {

            return false;
        }
    }

    /**
     * 递归处理json对象
     *
     * @param jsonSql            json sql{@link JsonSql}
     * @param arrayIdentifyValue 数组标识
     * @param kindPath           种类路径
     * @param i                  列的索引
     * @param obj                json对象
     * @param parentPath         父路径
     * <AUTHOR>
     * @date 2021/4/25 11:30
     */
    private void jsonObjectRecursive(JsonSql jsonSql, String arrayIdentifyValue, String kindPath, int i, Map<String, Object> obj, String parentPath) {

        final String jsonSqlMapPath = jsonSql.getPath();
        // 判断当前的字段类型，特殊的longtext，text无需length
        String type = Objects.nonNull(obj.get("type"))? obj.get("type").toString() : null;
        boolean notNeedLength = StringUtils.isNotEmpty(type) && ("longtext".equals(type) || "text".equals(type) || "double".equals(type));
        for (Map.Entry<String, Object> entry : obj.entrySet()) {

            final String objKey = entry.getKey();
            final Object objValue = entry.getValue();
            if (StringUtils.equals(objKey,"length") && notNeedLength) {
                // longtext，text无需length
                continue;
            }
            if (objValue instanceof Map) {

                @SuppressWarnings("unchecked")
                Map<String, Object> objectMap = (Map<String, Object>) objValue;
                jsonObjectRecursive(jsonSql, arrayIdentifyValue, kindPath, i, objectMap, objKey + "/");
            }

            if (jsonSqlMapPath.equals(kindPath + arrayIdentifyValue + "/" + StringUtils.defaultString(parentPath, "") + objKey)) {

                // 拼接对象的json路径获取具体的json值
                JsonKey jsonKey = jsonDefinition().getJsonKey(kindPath + arrayIdentifyValue + "/" + i + "/" + StringUtils.defaultString(parentPath, "") + objKey);
                Object jsonPathValue = jsonKey.getValue();
                // 占位符处理
                placeholderProcessor(jsonSql, jsonPathValue);
            }
        }
    }

    /**
     * 获取数组操作选项,默认为null,需要处理由子类实现
     *
     * <AUTHOR>
     * @date 2021/4/25 11:30
     */
    protected ArraysProcessorOptional acquireArraysProcessorOptional() {

        return null;
    }

    /**
     * 占位符处理
     *
     * @param jsonSql    {@link JsonSql}
     * @param sqlSegment sql片段
     * <AUTHOR>
     * @date 2021/4/25 11:30
     */
    protected abstract void placeholderProcessor(JsonSql jsonSql, Object sqlSegment);

    /**
     * 预准备操作
     *
     * <AUTHOR>
     * @date 2021/4/25 11:30
     */
    protected abstract void operationPrepare();
}
