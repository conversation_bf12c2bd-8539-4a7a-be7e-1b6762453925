package com.uino.x.pedestal.twin.jrm.core.sql.definition;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.uino.x.common.label.constant.StringConstant;
import com.uino.x.common.tool.base.StringUtils;
import com.uino.x.common.tool.spring.SpringEnvUtils;
import com.uino.x.pedestal.twin.jrm.core.domain.JsonSql;
import com.uino.x.pedestal.twin.jrm.core.enums.RequestType;
import com.uino.x.pedestal.twin.jrm.core.exception.JrmException;
import com.uino.x.pedestal.twin.jrm.core.json.definition.JsonDefinition;
import com.uino.x.pedestal.twin.jrm.core.sql.AbstractSqlBuilderProcessor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.sql.DataSource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ExecutionException;
import java.util.function.Supplier;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.uino.x.pedestal.twin.common.constant.Sql.SPACE;

/**
 * 抽象的sql描述信息
 *
 * <AUTHOR>
 * @version 0.0.1
 * @date 2021/4/16 16:07
 */
public abstract class AbstractSqlDefinition extends AbstractSqlBuilderProcessor implements SqlDefinition {

    /**
     * {@link Logger}
     */
    private static final Logger LOG = LoggerFactory.getLogger(AbstractSqlDefinition.class);

    /**
     * 本地的sql缓存<br>
     * 优先淘汰最近最少使用的条目（LRU）<br>
     * 同时考虑权重值，优先淘汰权重大的
     */
    private static final Cache<String, String> SQL_CACHE = CacheBuilder.newBuilder()
            .maximumWeight(50000)
            .softValues()
            .concurrencyLevel(Runtime.getRuntime().availableProcessors())
            .weigher((key, value) -> String.valueOf(key).length())
            .build();

    /**
     * sql标识-> 请求类型
     */
    protected final RequestType identify;

    /**
     * 预编译值列表
     */
    protected final List<Object> prepareValueList = new ArrayList<>();

    /**
     * 是否是预编译
     */
    protected boolean prepare;

    /**
     * 预编译数量
     */
    protected int prepareCount;

    /**
     * 构造方法
     *
     * @param dataSource     数据源
     * @param jsonDefinition json描述
     * @param identify       sql标识
     * <AUTHOR>
     * @date 2021/4/16 16:07
     */
    public AbstractSqlDefinition(DataSource dataSource, JsonDefinition jsonDefinition, RequestType identify) {

        super(dataSource, jsonDefinition);
        this.identify = identify;
    }

    @Override
    public String value() {
        Supplier<String> supplier = () -> {
            buildSql();
            return sql.toString();
        };
        String value;
        try {
            final String key = identify() + StringConstant.COLON + jsonDefinition().value();
            value = SQL_CACHE.get(key, supplier::get);
        } catch (ExecutionException e) {
            value = supplier.get();
        }
        LOG.debug("\nsql ===> \n{}", value);
        return value;
    }

    @Override
    public List<Object> prepareValueList() {
        return prepareValueList;
    }

    @Override
    public int prepareCount() {
        return (prepareCount = prepareValueList.size());
    }

    @Override
    public boolean prepare() {

        return (prepare = prepareCount() > 0);
    }

    @Override
    public RequestType identify() {
        return identify;
    }

    @Override
    public String quote() {
        if (SpringEnvUtils.isDameng()) {
            return "\"";
        }
        return "`";
    }

    /**
     * 获取sql标识名称
     *
     * @return sql标识名称
     * <AUTHOR>
     * @date 2021/4/16 16:07
     */
    protected String identifyName() {

        return identify().name().toLowerCase();
    }

    /**
     * 构建表名sql
     *
     * @return 表名sql
     * <AUTHOR>
     * @date 2021/4/16 16:07
     */
    protected String table() {
        // schema_name.table_name
        StringBuilder table = new StringBuilder();
        // 引号: ` "
        final String quote = quote();
        append(true, table, quote, convertJsonValueToSqlSegment(), quote + ".")
                .append(true, table, quote, convertJsonValueToSqlSegment(), quote);
        return table.toString();
    }

    @Override
    public void setPrepare(Object value) {

        prepareValueList.add(value);
    }

    @Override
    protected void operationPrepare() {
        append(identifyName()).append(SPACE);
    }

    static final Pattern VAR_PATTERN = Pattern.compile("\\{(.*?)\\}");

    @Override
    protected void placeholderProcessor(JsonSql jsonSql, Object sqlSegment) {
        if (Objects.isNull(jsonSql)) {
            return;
        }
        String jsonSqlMapValue = jsonSql.getValue();
        if (StringUtils.isBlank(jsonSqlMapValue)) {

            append(sqlSegment).append(SPACE);
        } else {
            // jsonSql的值不为空则检查是否包含占位符处理,拼接占位符左右两边sql片段
            if (StringUtils.isNotBlank(jsonSqlMapValue) && Objects.nonNull(sqlSegment)
                    && jsonSqlMapValue.contains(StringConstant.QUESTION_MARK)) {
                String[] attStrings = jsonSqlMapValue.split("\\?");
                final String leftAttString = attStrings[0];
                append(leftAttString).append(sqlSegment);
                if (attStrings.length > 1) {
                    append(attStrings[1]);
                }
                final Matcher matcher = VAR_PATTERN.matcher(jsonSqlMapValue);
                while (matcher.find()) {
                    final String var = matcher.group(1);
                    final String val = convertJsonValueToSqlSegment(JsonSql.ofPath(var));
                    if (StringUtils.isBlank(val)) {
                        throw new JrmException("Can't find variable " + var);
                    }
                    sql.replace(sql.indexOf("{"), sql.indexOf("}") + 1, val);
                }
                append(SPACE);
            }
        }
    }

    /**
     * 清除sql缓存
     *
     * <AUTHOR>
     */
    public void clearCache() {
        String identifyName = identify().name();
        String key = Objects.isNull(jsonDefinition()) ? identifyName : jsonDefinition().value() + identifyName;
        SQL_CACHE.invalidate(key);
    }
}
