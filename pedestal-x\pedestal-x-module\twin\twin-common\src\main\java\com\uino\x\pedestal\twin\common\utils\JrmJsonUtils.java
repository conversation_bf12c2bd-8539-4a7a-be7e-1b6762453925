package com.uino.x.pedestal.twin.common.utils;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.uino.x.common.pojo.util.IdUtils;
import com.uino.x.common.tool.spring.SpringEnvUtils;
import com.uino.x.pedestal.twin.common.constant.RequestKeyConstant;
import com.uino.x.pedestal.twin.common.enums.AutoTableTypeEnum;
import com.uino.x.pedestal.twin.pojo.domain.AlterColumn;
import com.uino.x.pedestal.twin.pojo.domain.Column;

import java.util.*;

/**
 * jrm json工具
 *
 * <AUTHOR>
 * @version 0.0.1
 * @date 2021/8/26 14:32
 */
public class JrmJsonUtils {

    /**
     * 表 key
     */
    public static final String TABLE_KEY = "table";

    /**
     * 名称 key
     */
    public static final String NAME_KEY = "name";

    /**
     * 新表 key
     */
    public static final String NEW_TABLE_KEY = "newTable";

    /**
     * 列 key
     */
    public static final String COLUMN_KEY = "column";

    /**
     * 版本key
     */
    public static final String VERSION_KEY = "@version";

    /**
     * 操作种类 key
     */
    public static final String KIND_KEY = "kind";

    /**
     * 已删除的表标识前缀
     */
    public static final String DELETED_FLAG_PREFIX = "deleted_";

    /**
     * 孪生体表标识前缀
     */
    public static final String TWIN_FLAG_PREFIX = "twin_";

    /**
     * 临时表标识前缀
     */
    public static final String TEMP_FLAG_PREFIX = "tmp_";

    /**
     * 创建Jrm create json
     *
     * @param table              表名
     * @param columnList         列的列表
     * @param version            jrm版本
     * @param excludeColumnNames 排除忽略的列名称
     * @return jrm参数json
     * <AUTHOR>
     * @date 2021/8/24 13:52
     */
    public static String createTableStructure(AutoTableTypeEnum tableType, String table, Integer version, List<Column> columnList, String... excludeColumnNames) {

        // 忽略的固定列
        List<String> excludeColumnNameList = new ArrayList<>(Arrays.asList(excludeColumnNames));
        excludeColumnNameList.add(ColumnUtils.CREATE_TIME_COLUMN);
        excludeColumnNameList.add(ColumnUtils.CREATE_USER_COLUMN);
        excludeColumnNameList.add(ColumnUtils.UPDATE_TIME_COLUMN);
        excludeColumnNameList.add(ColumnUtils.UPDATE_USER_COLUMN);
        final String[] excludeColumnNamesArray = excludeColumnNameList.stream().distinct().toArray(String[]::new);

        final List<Column> columns = new ArrayList<>();
        columns.addAll(ColumnUtils.initDefaultColumn(tableType, false,
                excludeColumnNamesArray
        ));
        columns.addAll(columnList);
        columns.addAll(ColumnUtils.initDefaultColumn(tableType, true,
                ColumnUtils.CREATE_TIME_COLUMN,
                ColumnUtils.CREATE_USER_COLUMN,
                ColumnUtils.UPDATE_TIME_COLUMN,
                ColumnUtils.UPDATE_USER_COLUMN));
        if (SpringEnvUtils.isDameng()) {
            columns.forEach(column -> {
                if (ColumnUtils.UPDATE_TIME_COLUMN.equals(column.getName())) {
                    column.setDefaultValue(column.getDefaultValue().replace(" on update current_timestamp", ""));
                }
            });
        }
        final Map<String, Object> paramMap = new LinkedHashMap<>(8);
//        paramMap.put(TABLE_KEY, table);
//        paramMap.put(COLUMN_KEY, columns);
//        paramMap.put(VERSION_KEY, version);

        final Map<String, Object> tableMap = new LinkedHashMap<>(8);
        tableMap.put(NAME_KEY, table);
        tableMap.put(COLUMN_KEY, columns);
        paramMap.put(TABLE_KEY, tableMap);
        paramMap.put(VERSION_KEY, version);

        setTransaction(paramMap);
        return JSON.toJSONString(paramMap);
    }

    /**
     * 转换新版本的嵌套结构
     */
    public static String convertNestedStructure(String structure) {
        JSONObject jsonObject = JSON.parseObject(structure);
        final Object table = jsonObject.remove(TABLE_KEY);
        if (table instanceof JSONObject) {
            return structure;
        }
        final Object columns = jsonObject.remove(COLUMN_KEY);
        final Map<String, Object> tableMap = new LinkedHashMap<>(8);
        tableMap.put(NAME_KEY, table);
        tableMap.put(COLUMN_KEY, columns);
        jsonObject.put(TABLE_KEY, tableMap);
        return jsonObject.toJSONString();
    }

    /**
     * 创建Jrm create json
     *
     * @param table              表名
     * @param columnList         列的列表
     * @param excludeColumnNames 忽略排除的列名称
     * @return jrm参数json
     * <AUTHOR>
     * @date 2021/8/24 13:52
     */
    public static String createTableStructure(AutoTableTypeEnum tableType, String table, List<Column> columnList, String... excludeColumnNames) {

        return createTableStructure(tableType, table, 1, columnList, excludeColumnNames);
    }

    /**
     * 创建默认字段的Jrm create json
     *
     * @param table 表名
     * @return jrm参数json
     * <AUTHOR>
     * @date 2021/8/24 13:52
     */
    public static String createDefaultTableStructure(AutoTableTypeEnum tableType, String table, String... excludeColumnNames) {

        return createTableStructure(tableType, table, Collections.emptyList(), excludeColumnNames);
    }

    /**
     * 创建Jrm alter json列表
     *
     * @param tableName       表名
     * @param version         jrm版本
     * @param kindType        alter操作类型
     * @param alterColumnList alter列列表
     * @return Jrm alter json列表
     * <AUTHOR>
     * @date 2021/8/24 13:52
     */
    public static List<String> createAlter(String tableName, Integer version, String kindType, List<AlterColumn> alterColumnList) {

        final LinkedHashMap<String, Object> alter = new LinkedHashMap<>(8);
        final List<String> list = new ArrayList<>();
        alter.put(TABLE_KEY, tableName);
        alter.put(VERSION_KEY, version);
        for (AlterColumn alterColumn : alterColumnList) {
            final LinkedHashMap<String, Column> kind = new LinkedHashMap<>(4);
            kind.put(kindType, alterColumn);
            alter.put(KIND_KEY, kind);
            setTransaction(alter);
            final String alterJrm = JSON.toJSONString(alter);
            list.add(alterJrm);
        }

        return list;
    }

    /**
     * 创建Jrm alter json列表
     *
     * @param tableName       表名
     * @param kindType        alter操作类型
     * @param alterColumnList alter列列表
     * @return Jrm alter json列表
     * <AUTHOR>
     * @date 2021/8/24 13:52
     */
    public static List<String> createAlter(String tableName, String kindType, List<AlterColumn> alterColumnList) {

        return createAlter(tableName, 1, kindType, alterColumnList);
    }

    /**
     * 根据表结构json信息获取表名
     *
     * @param structure 表结构json信息
     * @return 表名
     * <AUTHOR>
     * @date 2021/8/30 11:42
     */
    public static String getTableName(String structure) {

        final JSONObject jsonObject = JSON.parseObject(structure);
        final Object o = jsonObject.get(TABLE_KEY);
        if (o instanceof JSONObject table) {
            return table.getString(NAME_KEY);
        }
        return jsonObject.getString(TABLE_KEY);
    }

    /**
     * 根据表结构json信息获取字段数组
     *
     * @param structure 表结构json信息
     * @return 字段数组
     * <AUTHOR>
     * @date 2021/8/30 11:42
     */
    public static JSONArray getColumns(String structure) {

        final JSONObject jsonObject = JSON.parseObject(structure);
        final Object o = jsonObject.get(TABLE_KEY);
        if (o instanceof JSONObject table) {
            return table.getJSONArray(COLUMN_KEY);
        }
        return jsonObject.getJSONArray(COLUMN_KEY);
    }

    /**
     * 根据表结构json信息获取表名
     *
     * @param structure 表结构json信息
     * @return 表名
     * <AUTHOR>
     * @date 2021/8/30 11:42
     */
    public static Map<String, Object> getAlterTableNameObject(String structure) {

        final String tableName = getTableName(structure);
        final Map<String, Object> map = new LinkedHashMap<>(8);
        map.put(TABLE_KEY, tableName);
        map.put(VERSION_KEY, 1);
        return map;
    }

    /**
     * 创建修改表名的jrm alter json
     *
     * @param structure 表结构json信息
     * @param newTable  新的表名
     * @return 修改表名的jrm alter json
     * <AUTHOR>
     * @date 2021/8/30 11:42
     */
    public static String createAlterRenameTable(String structure, String newTable) {

        final Map<String, Object> tableNameObject = getAlterTableNameObject(structure);
        final LinkedHashMap<String, String> kind = new LinkedHashMap<>(4);
        kind.put(NEW_TABLE_KEY, newTable);
        tableNameObject.put(KIND_KEY, kind);
        setTransaction(tableNameObject);
        return JSON.toJSONString(tableNameObject);
    }

    /**
     * 设置jrm事务id
     *
     * @param jrmRequest jrm请求map
     * <AUTHOR>
     * @date 2021/9/21 13:42
     */
    public static void setTransaction(Map<String, Object> jrmRequest) {
        jrmRequest.put(RequestKeyConstant.SESSION_KEY, IdUtils.getIdStr());
    }
}